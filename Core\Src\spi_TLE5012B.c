/**
  ******************************************************************************
  * @file    spi_TLE5012B.c
  * <AUTHOR>
  * @version V3.5.0
  * @date    13-MAY-2017
  * @brief   This file contains the headers of the spi_TLE5012B.
  ******************************************************************************
  * @attention
  *
  *
  * <h2><center>&copy; COPYRIGHT 2017</center></h2>
  ******************************************************************************
  */
	
#include "spi_TLE5012B.h"
#include "modbus.h"
#include "crc_16.h"
#include "usart.h"
//#include "PublicSet.h"

//uint16_t TLE_Angle,Angle_Speed,TLE_Offset_Angle;
uint16_t TLE_angle=0,TLE_angle_b=0,Angle_data_ready=0,angle_crc;
u8 tx_buf[10]={0x01,0x02,0x03},rx_buf[10];
//spi_TLE5012B.H 中有简要说明

void Delay( uint16_t i )
{
   while( i-- );
}

uint16_t SPIx_ReadWriteByte(uint16_t byte)
{
	uint16_t retry = 0;
	while( (SPI1->SR&1<<1) == 0 )//发送缓冲区非空
	{
		if( ++retry > 200 )
			return 0;//延迟一段时间后返回
	}
	SPI1->DR = byte;     //发送数据
	
	retry = 0;
	while( (SPI1->SR&1<<0) == 0 ) //接收缓冲区为空
	{
		if( ++retry > 200 )
			return 0;//延迟一段时间后返回
	}
	return SPI1->DR;          //读一下缓冲区，清标志
}


//得到 0~359 度
uint16_t ReadAngle(void)
{
	return ( ReadValue(READ_ANGLE_VALUE) * 360 / 0x10000 );
}

//得到角速度
uint16_t ReadSpeed(void)
{
	return ReadValue(READ_SPEED_VALUE);
}


uint16_t ReadValue(uint16_t u16RegValue)
{
	uint16_t u16Data;

	SPI_CS_ENABLE;
	
	SPIx_ReadWriteByte(u16RegValue);
  SPI_TX_OFF();
	
	//发送 0xFFFF 是无用的，可能是为了有时钟
	u16Data = ( SPIx_ReadWriteByte(0xffff) & 0x7FFF )<<1;//0x12/0xff*100k
	
	SPI_CS_DISABLE;
  SPI_TX_ON();
	
	return(u16Data);
}

void SPI_TX_ON(void)
{
	/*
	GPIO_InitTypeDef GPIO_InitStruct = {0};

  GPIO_InitStruct.Pin = GPIO_PIN_7;
  GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
	
  HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
	*/
	GPIOA->CRL&=0xBFFFFFFF;
}

void SPI_TX_OFF(void)
{
	/*
	GPIO_InitTypeDef GPIO_InitStruct = {0};

  GPIO_InitStruct.Pin = GPIO_PIN_7;
  GPIO_InitStruct.Mode = GPIO_MODE_AF_OD;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
	
  HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
	*/
	GPIOA->CRL|=0x40000000;
}

void update_angle_to_485(void)
{
	
}
void update_angle(void)
{
		t2=SysTick->VAL;
		tt3=tt1-t2;
		tt1=t2;
		if(tt3>tt4)
			tt4=tt3;
		//TLE_angle = ReadAngle();
		TLE_angle = ReadValue(READ_ANGLE_VALUE);
		tx_buf[0] = TLE_angle>>8;
		tx_buf[1] = TLE_angle&0xff;
		angle_crc =Get_Crc16(tx_buf,2);
		//tx_buf[3] = angle_crc&0xff;
		//tx_buf[4] = angle_crc>>8;
		t1=SysTick->VAL;
		t3=t2-t1;
		if(t3>t4)
			t4=t3;
		Angle_data_ready=1;
	
}

